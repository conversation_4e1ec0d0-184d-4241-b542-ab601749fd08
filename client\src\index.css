@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(224, 71.4%, 4.1%);
  --muted: hsl(220, 14.3%, 95.9%);
  --muted-foreground: hsl(220, 8.9%, 46.1%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(224, 71.4%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(224, 71.4%, 4.1%);
  --border: hsl(220, 13%, 91%);
  --input: hsl(220, 13%, 91%);
  --primary: hsl(239, 84%, 67%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(158, 64%, 52%);
  --secondary-foreground: hsl(210, 40%, 98%);
  --accent: hsl(45, 93%, 47%);
  --accent-foreground: hsl(210, 40%, 98%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(224, 71.4%, 4.1%);
  --radius: 0.5rem;

  /* Chart colors */
  --chart-1: hsl(12, 76%, 61%);
  --chart-2: hsl(173, 58%, 39%);
  --chart-3: hsl(197, 37%, 24%);
  --chart-4: hsl(43, 74%, 66%);
  --chart-5: hsl(27, 87%, 67%);

  /* Custom colors for church app */
  --church-primary: hsl(239, 84%, 67%);
  --church-secondary: hsl(158, 64%, 52%);
  --church-accent: hsl(45, 93%, 47%);
  --church-neutral: hsl(224, 71.4%, 4.1%);
  --church-success: hsl(142, 76%, 36%);
  --church-warning: hsl(45, 93%, 47%);
  --church-error: hsl(0, 84%, 60%);
}

.dark {
  --background: hsl(224, 71.4%, 4.1%);
  --foreground: hsl(210, 40%, 98%);
  --muted: hsl(223, 47%, 11%);
  --muted-foreground: hsl(215.4, 16.3%, 56.9%);
  --popover: hsl(224, 71.4%, 4.1%);
  --popover-foreground: hsl(210, 40%, 98%);
  --card: hsl(224, 71.4%, 4.1%);
  --card-foreground: hsl(210, 40%, 98%);
  --border: hsl(216, 12.2%, 16.9%);
  --input: hsl(216, 12.2%, 16.9%);
  --primary: hsl(239, 84%, 67%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(158, 64%, 52%);
  --secondary-foreground: hsl(210, 40%, 98%);
  --accent: hsl(45, 93%, 47%);
  --accent-foreground: hsl(210, 40%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(216, 34.4%, 53.9%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

@layer utilities {
  .notification-badge {
    animation: notificationPulse 2s infinite;
    font-size: 10px;
    line-height: 1;
    min-width: 18px;
    min-height: 18px;
  }
  
  @keyframes notificationPulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.2);
      opacity: 0.8;
      box-shadow: 0 0 8px rgba(239, 68, 68, 0.6);
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Clock alarm animation for urgent notifications */
  @keyframes bellRing {
    0%, 100% { transform: rotate(0deg); }
    10%, 30%, 50%, 70%, 90% { transform: rotate(15deg); }
    20%, 40%, 60%, 80% { transform: rotate(-15deg); }
  }

  .notification-bell-urgent {
    animation: bellRing 1s ease-in-out infinite;
  }
  
  .task-priority-high {
    border-left: 4px solid hsl(var(--destructive));
  }
  
  .task-priority-medium {
    border-left: 4px solid hsl(var(--accent));
  }
  
  .task-priority-low {
    border-left: 4px solid hsl(var(--secondary));
  }
  
  .member-card:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease-in-out;
  }
}
