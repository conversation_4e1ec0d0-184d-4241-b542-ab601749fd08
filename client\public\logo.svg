<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6366F1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="crossGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F8FAFC;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Main background -->
  <circle cx="20" cy="20" r="20" fill="url(#bgGradient)"/>
  
  <!-- Cross symbol (faith element) -->
  <path d="M20 8v24M12 16h16" stroke="url(#crossGradient)" stroke-width="3" stroke-linecap="round"/>
  
  <!-- Progress tracking dots (tracking element) -->
  <circle cx="14" cy="26" r="1.5" fill="#FFFFFF" opacity="0.8"/>
  <circle cx="18" cy="28" r="1.5" fill="#FFFFFF" opacity="0.9"/>
  <circle cx="22" cy="28" r="1.5" fill="#FFFFFF"/>
  <circle cx="26" cy="26" r="1.5" fill="#FFFFFF" opacity="0.7"/>
  
  <!-- Subtle inner glow -->
  <circle cx="20" cy="20" r="18" fill="none" stroke="#FFFFFF" stroke-width="0.5" opacity="0.3"/>
</svg>
