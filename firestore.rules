rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read and write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Members collection - authenticated users can read/write
    match /members/{document} {
      allow read, write: if request.auth != null;
    }
    
    // Tasks collection - authenticated users can read/write
    match /tasks/{document} {
      allow read, write: if request.auth != null;
    }
    
    // Follow-ups collection - authenticated users can read/write
    match /followUps/{document} {
      allow read, write: if request.auth != null;
    }
  }
}
