# Client-side Firebase Configuration (for Vite)
# Replace these with your actual Firebase web app config values
VITE_FIREBASE_API_KEY=your_firebase_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
VITE_FIREBASE_MEASUREMENT_ID=your_measurement_id

# Server-side Firebase Configuration
# Replace YOUR_PROJECT_ID with your actual project ID
FIREBASE_PROJECT_ID=your-project-id
# Replace the entire JSON content below with your downloaded service account JSON (all on one line)
# DO NOT commit actual service account credentials to git!
# Set this in your deployment environment or use a local .env.local file
FIREBASE_SERVICE_ACCOUNT_KEY=your_service_account_json_here


# Application Configuration
NODE_ENV=development
