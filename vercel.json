{"version": 2, "builds": [{"src": "package.json", "use": "@vercel/node", "config": {"includeFiles": ["dist/**", "client/public/**", "shared/**"]}}], "routes": [{"src": "/api/(.*)", "dest": "/dist/index.js"}, {"src": "/manifest.json", "dest": "/client/public/manifest.json"}, {"src": "/favicon.svg", "dest": "/client/public/favicon.svg"}, {"src": "/logo.svg", "dest": "/client/public/logo.svg"}, {"src": "/(.*)", "dest": "/dist/public/$1"}, {"src": "/(.*)", "dest": "/dist/public/index.html"}], "functions": {"dist/index.js": {"maxDuration": 30}}, "env": {"NODE_ENV": "production"}, "headers": [{"source": "/manifest.json", "headers": [{"key": "Content-Type", "value": "application/manifest+json"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}